#!/usr/bin/env python3
"""
اختبار دعم روابط المجموعات الخاصة في تليجرام
"""

import re

def test_private_chat_links():
    """اختبار تحليل روابط المجموعات الخاصة"""
    
    # روابط للاختبار
    test_links = [
        # روابط المجموعات الخاصة
        "https://t.me/c/2335296786/41",
        "t.me/c/2335296786/41",
        "https://t.me/c/1234567890/123",
        
        # روابط المجموعات الخاصة مع مواضيع
        "https://t.me/c/2335296786/13/5428",
        "t.me/c/2335296786/13/5428",
        
        # روابط عادية للمقارنة
        "https://t.me/MasterfulCommunity/31",
        "https://t.me/MasterfulCommunity/13/5428",
        
        # روابط خاطئة
        "https://t.me/c/",
        "https://t.me/c/abc/123",
        "invalid_link",
    ]
    
    print("🔍 اختبار تحليل روابط المجموعات الخاصة:")
    print("=" * 70)
    
    for url in test_links:
        print(f"\n📎 الرابط: {url}")
        
        # نفس الكود المستخدم في البوت
        post_match = (
            # روابط المجموعات الخاصة: https://t.me/c/chat_id/message_id
            re.match(r'https?://t\.me/c/(\d+)/(\d+)', url) or
            re.match(r't\.me/c/(\d+)/(\d+)', url) or
            # روابط المجموعات الخاصة مع مواضيع: https://t.me/c/chat_id/topic_id/message_id
            re.match(r'https?://t\.me/c/(\d+)/(\d+)/(\d+)', url) or
            re.match(r't\.me/c/(\d+)/(\d+)/(\d+)', url) or
            # روابط مع مواضيع: https://t.me/channel/topic_id/message_id
            re.match(r'https?://t\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r'https?://telegram\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r'telegram\.me/([^/]+)/(\d+)/(\d+)', url) or
            # روابط عادية: https://t.me/channel/message_id
            re.match(r'https?://t\.me/([^/]+)/(\d+)', url) or
            re.match(r'https?://telegram\.me/([^/]+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)', url) or
            re.match(r'telegram\.me/([^/]+)/(\d+)', url)
        )
        
        if post_match:
            groups = post_match.groups()
            topic_id = None
            
            # تحديد نوع الرابط
            if '/c/' in url:
                # رابط مجموعة خاصة
                chat_id = int(post_match.group(1))
                channel_username = f"-100{chat_id}"
                
                if len(groups) == 3:
                    # مجموعة خاصة مع موضوع
                    topic_id = int(post_match.group(2))
                    message_id = int(post_match.group(3))
                    print(f"✅ رابط مجموعة خاصة مع موضوع:")
                    print(f"   🆔 معرف المجموعة: {channel_username}")
                    print(f"   📁 الموضوع: {topic_id}")
                    print(f"   📄 المنشور: {message_id}")
                else:
                    # مجموعة خاصة عادية
                    message_id = int(post_match.group(2))
                    print(f"✅ رابط مجموعة خاصة:")
                    print(f"   🆔 معرف المجموعة: {channel_username}")
                    print(f"   📄 المنشور: {message_id}")
            else:
                # رابط عادي
                channel_username = post_match.group(1)
                
                if len(groups) == 3:
                    topic_id = int(post_match.group(2))
                    message_id = int(post_match.group(3))
                    print(f"✅ رابط عادي مع موضوع:")
                    print(f"   📺 القناة: {channel_username}")
                    print(f"   📁 الموضوع: {topic_id}")
                    print(f"   📄 المنشور: {message_id}")
                else:
                    message_id = int(post_match.group(2))
                    print(f"✅ رابط عادي:")
                    print(f"   📺 القناة: {channel_username}")
                    print(f"   📄 المنشور: {message_id}")
        else:
            print("❌ رابط غير صالح")

def test_specific_link():
    """اختبار الرابط المحدد من المستخدم"""
    
    specific_link = "https://t.me/c/2335296786/41"
    
    print("\n\n🎯 اختبار الرابط المحدد:")
    print("=" * 50)
    print(f"📎 الرابط: {specific_link}")
    
    # تحليل الرابط
    post_match = re.match(r'https?://t\.me/c/(\d+)/(\d+)', specific_link)
    
    if post_match:
        chat_id = int(post_match.group(1))
        message_id = int(post_match.group(2))
        channel_username = f"-100{chat_id}"
        
        print(f"✅ تم التعرف على الرابط:")
        print(f"   🔢 معرف المجموعة الأصلي: {chat_id}")
        print(f"   🆔 معرف المجموعة الكامل: {channel_username}")
        print(f"   📄 معرف المنشور: {message_id}")
        print(f"   💡 نوع الرابط: مجموعة خاصة")
        
        # تحويل المعرف
        print(f"\n🔄 تحويل المعرف:")
        print(f"   من: {chat_id}")
        print(f"   إلى: {channel_username}")
        print(f"   السبب: تليجرام يستخدم معرفات سالبة للمجموعات")
        
    else:
        print("❌ لم يتم التعرف على الرابط")

def explain_private_chat_format():
    """شرح تنسيق روابط المجموعات الخاصة"""
    
    print("\n\n📚 شرح تنسيق روابط المجموعات الخاصة:")
    print("=" * 60)
    
    print("\n🔗 تنسيق الرابط:")
    print("   https://t.me/c/[CHAT_ID]/[MESSAGE_ID]")
    print("   https://t.me/c/[CHAT_ID]/[TOPIC_ID]/[MESSAGE_ID]")
    
    print("\n📋 مكونات الرابط:")
    print("   • /c/ = يشير إلى مجموعة خاصة")
    print("   • CHAT_ID = معرف المجموعة الرقمي")
    print("   • TOPIC_ID = معرف الموضوع (اختياري)")
    print("   • MESSAGE_ID = معرف المنشور")
    
    print("\n🔄 تحويل المعرف:")
    print("   • معرف الرابط: 2335296786")
    print("   • معرف تليجرام: -1002335296786")
    print("   • القاعدة: -100 + معرف الرابط")
    
    print("\n💡 الفرق بين الأنواع:")
    print("   📺 قناة عامة: https://t.me/channel_name/123")
    print("   👥 مجموعة عامة: https://t.me/group_name/123")
    print("   🔒 مجموعة خاصة: https://t.me/c/1234567890/123")

if __name__ == "__main__":
    print("🧪 اختبار دعم روابط المجموعات الخاصة في تليجرام")
    print("=" * 70)
    
    # تشغيل الاختبارات
    test_private_chat_links()
    test_specific_link()
    explain_private_chat_format()
    
    print("\n\n🎉 انتهى الاختبار!")
    print("💡 الآن البوت يدعم:")
    print("   ✅ القنوات العامة: https://t.me/channel/message")
    print("   ✅ المجموعات العامة: https://t.me/group/message")
    print("   ✅ المجموعات الخاصة: https://t.me/c/chat_id/message")
    print("   ✅ جميع الأنواع مع المواضيع (Topics)")
    print("   ✅ جميع تنسيقات الروابط (https, t.me, telegram.me)")
