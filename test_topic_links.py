#!/usr/bin/env python3
"""
اختبار دعم روابط المواضيع (Topics) في تليجرام
"""

import re

def test_link_parsing():
    """اختبار تحليل الروابط المختلفة"""
    
    # روابط للاختبار
    test_links = [
        # روابط عادية
        "https://t.me/MasterfulCommunity/31",
        "t.me/MasterfulCommunity/31",
        "https://telegram.me/MasterfulCommunity/31",
        
        # روابط مع مواضيع (Topics)
        "https://t.me/MasterfulCommunity/13/5428",
        "t.me/MasterfulCommunity/13/5428",
        "https://telegram.me/MasterfulCommunity/13/5428",
        
        # روابط خاطئة
        "https://t.me/MasterfulCommunity",
        "https://t.me/MasterfulCommunity/",
        "invalid_link",
    ]
    
    print("🔍 اختبار تحليل الروابط:")
    print("=" * 60)
    
    for url in test_links:
        print(f"\n📎 الرابط: {url}")
        
        # نفس الكود المستخدم في البوت
        post_match = (
            # روابط مع مواضيع: https://t.me/channel/topic_id/message_id
            re.match(r'https?://t\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)/(\d+)', url) or
            # روابط عادية: https://t.me/channel/message_id
            re.match(r'https?://t\.me/([^/]+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)', url)
        )
        
        if post_match:
            channel_username = post_match.group(1)
            
            # التحقق من وجود معرف موضوع
            if len(post_match.groups()) == 3:
                topic_id = int(post_match.group(2))
                message_id = int(post_match.group(3))
                print(f"✅ رابط مع موضوع:")
                print(f"   📺 القناة: {channel_username}")
                print(f"   📁 الموضوع: {topic_id}")
                print(f"   📄 المنشور: {message_id}")
            else:
                message_id = int(post_match.group(2))
                print(f"✅ رابط عادي:")
                print(f"   📺 القناة: {channel_username}")
                print(f"   📄 المنشور: {message_id}")
        else:
            print("❌ رابط غير صالح")

def test_specific_links():
    """اختبار الروابط المحددة من المستخدم"""
    
    specific_links = [
        "https://t.me/MasterfulCommunity",
        "https://t.me/MasterfulCommunity/31",
        "https://t.me/MasterfulCommunity/13/5428"
    ]
    
    print("\n\n🎯 اختبار الروابط المحددة:")
    print("=" * 60)
    
    for url in specific_links:
        print(f"\n📎 الرابط: {url}")
        
        # تحليل الرابط
        post_match = (
            re.match(r'https?://t\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)/(\d+)', url) or
            re.match(r'https?://t\.me/([^/]+)/(\d+)', url) or
            re.match(r't\.me/([^/]+)/(\d+)', url)
        )
        
        if post_match:
            channel = post_match.group(1)
            groups = post_match.groups()
            
            print(f"✅ تم التعرف على الرابط:")
            print(f"   📺 القناة: {channel}")
            print(f"   🔢 عدد المجموعات: {len(groups)}")
            
            if len(groups) == 3:
                topic_id = int(post_match.group(2))
                message_id = int(post_match.group(3))
                print(f"   📁 معرف الموضوع: {topic_id}")
                print(f"   📄 معرف المنشور: {message_id}")
                print(f"   💡 نوع الرابط: رابط مع موضوع (Topic)")
            elif len(groups) == 2:
                message_id = int(post_match.group(2))
                print(f"   📄 معرف المنشور: {message_id}")
                print(f"   💡 نوع الرابط: رابط عادي")
        else:
            print("❌ لم يتم التعرف على الرابط")
            
            # تحليل سبب الفشل
            if "/t.me/" in url or url.startswith("t.me/"):
                if url.count("/") < 2:
                    print("   🔍 السبب: الرابط لا يحتوي على معرف منشور")
                else:
                    print("   🔍 السبب: تنسيق الرابط غير مدعوم")
            else:
                print("   🔍 السبب: ليس رابط تليجرام صالح")

if __name__ == "__main__":
    print("🧪 اختبار دعم روابط المواضيع في تليجرام")
    print("=" * 60)
    
    # تشغيل الاختبارات
    test_link_parsing()
    test_specific_links()
    
    print("\n\n🎉 انتهى الاختبار!")
    print("💡 الآن البوت يدعم:")
    print("   ✅ الروابط العادية: https://t.me/channel/message_id")
    print("   ✅ روابط المواضيع: https://t.me/channel/topic_id/message_id")
