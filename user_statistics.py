"""
نظام إحصائيات المستخدمين المتقدم
يتتبع جميع أنشطة المستخدمين ويوفر إحصائيات مفصلة
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class UserStatistics:
    """مدير إحصائيات المستخدمين"""

    def __init__(self, stats_file: str = "user_statistics.json", excluded_user_ids: set = None):
        """
        تهيئة نظام الإحصائيات

        Args:
            stats_file (str): مسار ملف الإحصائيات
            excluded_user_ids (set): معرفات المستخدمين المستثناة من الإحصائيات
        """
        self.stats_file = stats_file
        self.excluded_user_ids = excluded_user_ids or {6719024416}  # حسابك الشخصي
        self.stats_data = self._load_stats_data()

        # إحصائيات اليوم
        self.today = datetime.now().strftime("%Y-%m-%d")

        # التأكد من وجود بيانات اليوم
        if self.today not in self.stats_data.get("daily_stats", {}):
            self._init_daily_stats()
    
    def _load_stats_data(self) -> Dict:
        """تحميل بيانات الإحصائيات من الملف"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            
            # إنشاء ملف إحصائيات جديد
            default_data = {
                "total_users": 0,
                "users": {},
                "daily_stats": {},
                "monthly_stats": {},
                "bot_started": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            with open(self.stats_file, "w", encoding="utf-8") as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
            
            return default_data
            
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الإحصائيات: {e}")
            return {}
    
    def _save_stats_data(self) -> bool:
        """حفظ بيانات الإحصائيات إلى الملف"""
        try:
            self.stats_data["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.stats_file, "w", encoding="utf-8") as f:
                json.dump(self.stats_data, f, ensure_ascii=False, indent=2)
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الإحصائيات: {e}")
            return False
    
    def _init_daily_stats(self):
        """تهيئة إحصائيات اليوم"""
        if "daily_stats" not in self.stats_data:
            self.stats_data["daily_stats"] = {}
        
        self.stats_data["daily_stats"][self.today] = {
            "new_users": 0,
            "active_users": 0,
            "total_commands": 0,
            "single_posts": 0,
            "range_posts": 0,
            "forward_posts": 0,
            "backward_posts": 0,
            "vip_actions": 0,
            "errors": 0
        }
        
        self._save_stats_data()
    
    def add_new_user(self, user_id: int, username: str = None, first_name: str = None, last_name: str = None) -> bool:
        """إضافة مستخدم جديد"""
        try:
            user_id_str = str(user_id)
            
            # التحقق من أن المستخدم جديد
            if user_id_str in self.stats_data.get("users", {}):
                return False  # المستخدم موجود مسبقاً
            
            # إضافة المستخدم الجديد
            if "users" not in self.stats_data:
                self.stats_data["users"] = {}
            
            self.stats_data["users"][user_id_str] = {
                "user_id": user_id,
                "username": username,
                "first_name": first_name,
                "last_name": last_name,
                "joined_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_activity": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_commands": 0,
                "single_posts": 0,
                "range_posts": 0,
                "forward_posts": 0,
                "backward_posts": 0,
                "vip_actions": 0,
                "is_vip": False,
                "is_active": True
            }
            
            # تحديث العدد الإجمالي
            self.stats_data["total_users"] = len(self.stats_data["users"])
            
            # تحديث إحصائيات اليوم
            if self.today not in self.stats_data.get("daily_stats", {}):
                self._init_daily_stats()
            
            self.stats_data["daily_stats"][self.today]["new_users"] += 1
            
            self._save_stats_data()
            logger.info(f"تم إضافة مستخدم جديد: {user_id} ({first_name})")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة مستخدم جديد {user_id}: {e}")
            return False
    
    def update_user_activity(self, user_id: int, action: str = "command") -> bool:
        """تحديث نشاط المستخدم"""
        try:
            user_id_str = str(user_id)
            
            # التأكد من وجود المستخدم
            if user_id_str not in self.stats_data.get("users", {}):
                return False
            
            # تحديث آخر نشاط
            self.stats_data["users"][user_id_str]["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.stats_data["users"][user_id_str]["total_commands"] += 1
            
            # تحديث نوع النشاط المحدد
            if action in ["single_posts", "range_posts", "forward_posts", "backward_posts", "vip_actions"]:
                self.stats_data["users"][user_id_str][action] += 1
            
            # تحديث إحصائيات اليوم
            if self.today not in self.stats_data.get("daily_stats", {}):
                self._init_daily_stats()
            
            self.stats_data["daily_stats"][self.today]["total_commands"] += 1
            
            if action in ["single_posts", "range_posts", "forward_posts", "backward_posts", "vip_actions"]:
                self.stats_data["daily_stats"][self.today][action] += 1
            
            # تحديث المستخدمين النشطين اليوم
            active_today = set()
            for uid, user_data in self.stats_data["users"].items():
                last_activity = user_data.get("last_activity", "")
                if last_activity.startswith(self.today):
                    active_today.add(uid)
            
            self.stats_data["daily_stats"][self.today]["active_users"] = len(active_today)
            
            self._save_stats_data()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث نشاط المستخدم {user_id}: {e}")
            return False
    
    def get_total_users(self) -> int:
        """الحصول على العدد الإجمالي للمستخدمين"""
        return self.stats_data.get("total_users", 0)
    
    def get_active_users_today(self) -> int:
        """الحصول على عدد المستخدمين النشطين اليوم"""
        try:
            return self.stats_data.get("daily_stats", {}).get(self.today, {}).get("active_users", 0)
        except:
            return 0
    
    def get_new_users_today(self) -> int:
        """الحصول على عدد المستخدمين الجدد اليوم"""
        try:
            return self.stats_data.get("daily_stats", {}).get(self.today, {}).get("new_users", 0)
        except:
            return 0
    
    def get_daily_stats(self, date: str = None) -> Dict:
        """الحصول على إحصائيات يوم محدد"""
        if date is None:
            date = self.today
        
        return self.stats_data.get("daily_stats", {}).get(date, {})
    
    def get_weekly_stats(self) -> Dict:
        """الحصول على إحصائيات الأسبوع الماضي"""
        try:
            weekly_stats = {
                "total_users": 0,
                "new_users": 0,
                "active_users": set(),
                "total_commands": 0,
                "single_posts": 0,
                "range_posts": 0,
                "forward_posts": 0,
                "backward_posts": 0,
                "vip_actions": 0
            }
            
            # آخر 7 أيام
            for i in range(7):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                day_stats = self.stats_data.get("daily_stats", {}).get(date, {})
                
                weekly_stats["new_users"] += day_stats.get("new_users", 0)
                weekly_stats["total_commands"] += day_stats.get("total_commands", 0)
                weekly_stats["single_posts"] += day_stats.get("single_posts", 0)
                weekly_stats["range_posts"] += day_stats.get("range_posts", 0)
                weekly_stats["forward_posts"] += day_stats.get("forward_posts", 0)
                weekly_stats["backward_posts"] += day_stats.get("backward_posts", 0)
                weekly_stats["vip_actions"] += day_stats.get("vip_actions", 0)
                
                # حساب المستخدمين النشطين في الأسبوع
                for uid, user_data in self.stats_data.get("users", {}).items():
                    last_activity = user_data.get("last_activity", "")
                    if last_activity.startswith(date):
                        weekly_stats["active_users"].add(uid)
            
            weekly_stats["active_users"] = len(weekly_stats["active_users"])
            weekly_stats["total_users"] = self.get_total_users()
            
            return weekly_stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الأسبوع: {e}")
            return {}
    
    def get_top_users(self, limit: int = 10) -> List[Dict]:
        """الحصول على أكثر المستخدمين نشاطاً (مع استثناء المشرفين)"""
        try:
            users = []
            for uid, user_data in self.stats_data.get("users", {}).items():
                user_id = user_data.get("user_id")

                # استثناء المشرفين والحسابات المحددة
                if user_id in self.excluded_user_ids:
                    continue

                users.append({
                    "user_id": user_id,
                    "name": user_data.get("first_name", "غير معروف"),
                    "username": user_data.get("username"),
                    "total_commands": user_data.get("total_commands", 0),
                    "joined_date": user_data.get("joined_date"),
                    "last_activity": user_data.get("last_activity")
                })

            # ترتيب حسب عدد الأوامر
            users.sort(key=lambda x: x["total_commands"], reverse=True)

            return users[:limit]
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أكثر المستخدمين نشاطاً: {e}")
            return []
    
    def get_bot_uptime(self) -> str:
        """حساب مدة تشغيل البوت"""
        try:
            bot_started = self.stats_data.get("bot_started")
            if not bot_started:
                return "غير معروف"
            
            start_time = datetime.strptime(bot_started, "%Y-%m-%d %H:%M:%S")
            uptime = datetime.now() - start_time
            
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            if days > 0:
                return f"{days} يوم، {hours} ساعة، {minutes} دقيقة"
            elif hours > 0:
                return f"{hours} ساعة، {minutes} دقيقة"
            else:
                return f"{minutes} دقيقة"
                
        except Exception as e:
            logger.error(f"خطأ في حساب مدة التشغيل: {e}")
            return "غير معروف"
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime("%Y-%m-%d")
            
            # حذف الإحصائيات اليومية القديمة
            daily_stats = self.stats_data.get("daily_stats", {})
            dates_to_remove = [date for date in daily_stats.keys() if date < cutoff_date]
            
            for date in dates_to_remove:
                del daily_stats[date]
            
            logger.info(f"تم حذف {len(dates_to_remove)} يوم من الإحصائيات القديمة")
            
            self._save_stats_data()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {e}")
            return False

# إنشاء مثيل عام للإحصائيات
user_stats = UserStatistics(excluded_user_ids={6719024416})
