"""
نظام تحويل الملفات المتقدم
يدعم تحويل الصور، الفيديو، الصوت، والمستندات
"""

import os
import time
import logging
import tempfile
from typing import Optional, Tuple
from pathlib import Path

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class FileConverter:
    """محول الملفات المتقدم"""
    
    def __init__(self):
        """تهيئة محول الملفات"""
        self.temp_dir = tempfile.mkdtemp()
        self.supported_formats = {
            'image': ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp', 'tiff', 'ico'],
            'video': ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', '3gp', 'wmv'],
            'audio': ['mp3', 'wav', 'ogg', 'm4a', 'flac', 'aac', 'wma'],
            'document': ['pdf', 'docx', 'txt', 'html', 'rtf']
        }
    
    def get_file_type(self, filename: str) -> Optional[str]:
        """تحديد نوع الملف"""
        try:
            extension = Path(filename).suffix.lower().lstrip('.')
            for file_type, extensions in self.supported_formats.items():
                if extension in extensions:
                    return file_type
            return None
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع الملف: {e}")
            return None
    
    def is_supported_format(self, filename: str, target_format: str) -> bool:
        """التحقق من دعم التحويل"""
        file_type = self.get_file_type(filename)
        if not file_type:
            return False
        
        target_format = target_format.lower().lstrip('.')
        return target_format in self.supported_formats.get(file_type, [])
    
    def get_conversion_options(self, filename: str) -> list:
        """الحصول على خيارات التحويل المتاحة"""
        file_type = self.get_file_type(filename)
        if not file_type:
            return []
        
        current_format = Path(filename).suffix.lower().lstrip('.')
        available_formats = self.supported_formats.get(file_type, [])
        
        # إزالة الصيغة الحالية من القائمة
        return [fmt for fmt in available_formats if fmt != current_format]
    
    async def convert_image(self, input_path: str, output_format: str, quality: int = 95) -> Optional[str]:
        """تحويل الصور"""
        try:
            from PIL import Image
            
            # فتح الصورة
            with Image.open(input_path) as img:
                # تحويل RGBA إلى RGB إذا كانت الصيغة المطلوبة لا تدعم الشفافية
                if output_format.lower() in ['jpg', 'jpeg'] and img.mode in ['RGBA', 'LA']:
                    # إنشاء خلفية بيضاء
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                # إنشاء مسار الملف الجديد
                output_path = os.path.join(
                    self.temp_dir,
                    f"converted_{int(time.time())}.{output_format}"
                )
                
                # حفظ الصورة بالصيغة الجديدة
                save_kwargs = {}
                if output_format.lower() in ['jpg', 'jpeg']:
                    save_kwargs['quality'] = quality
                    save_kwargs['optimize'] = True
                elif output_format.lower() == 'png':
                    save_kwargs['optimize'] = True
                elif output_format.lower() == 'webp':
                    save_kwargs['quality'] = quality
                    save_kwargs['method'] = 6
                
                img.save(output_path, format=output_format.upper(), **save_kwargs)
                
                logger.info(f"تم تحويل الصورة من {input_path} إلى {output_path}")
                return output_path
                
        except ImportError:
            logger.error("مكتبة Pillow غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل الصورة: {e}")
            return None
    
    async def convert_video(self, input_path: str, output_format: str, quality: str = "medium") -> Optional[str]:
        """تحويل الفيديو"""
        try:
            import ffmpeg
            
            # إنشاء مسار الملف الجديد
            output_path = os.path.join(
                self.temp_dir,
                f"converted_{int(time.time())}.{output_format}"
            )
            
            # إعدادات الجودة
            quality_settings = {
                'low': {'crf': 28, 'preset': 'fast'},
                'medium': {'crf': 23, 'preset': 'medium'},
                'high': {'crf': 18, 'preset': 'slow'}
            }
            
            settings = quality_settings.get(quality, quality_settings['medium'])
            
            # تحويل الفيديو
            stream = ffmpeg.input(input_path)
            stream = ffmpeg.output(
                stream, 
                output_path,
                vcodec='libx264',
                acodec='aac',
                crf=settings['crf'],
                preset=settings['preset']
            )
            
            ffmpeg.run(stream, overwrite_output=True, quiet=True)
            
            logger.info(f"تم تحويل الفيديو من {input_path} إلى {output_path}")
            return output_path
            
        except ImportError:
            logger.error("مكتبة ffmpeg-python غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل الفيديو: {e}")
            return None
    
    async def convert_audio(self, input_path: str, output_format: str, bitrate: str = "192k") -> Optional[str]:
        """تحويل الصوت"""
        try:
            from pydub import AudioSegment
            
            # تحميل الملف الصوتي
            audio = AudioSegment.from_file(input_path)
            
            # إنشاء مسار الملف الجديد
            output_path = os.path.join(
                self.temp_dir,
                f"converted_{int(time.time())}.{output_format}"
            )
            
            # تحديد معاملات التصدير
            export_params = {}
            if output_format.lower() == 'mp3':
                export_params['bitrate'] = bitrate
            elif output_format.lower() == 'ogg':
                export_params['codec'] = 'libvorbis'
            
            # تصدير الملف
            audio.export(output_path, format=output_format, **export_params)
            
            logger.info(f"تم تحويل الصوت من {input_path} إلى {output_path}")
            return output_path
            
        except ImportError:
            logger.error("مكتبة pydub غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل الصوت: {e}")
            return None
    
    async def convert_document(self, input_path: str, output_format: str) -> Optional[str]:
        """تحويل المستندات"""
        try:
            input_format = Path(input_path).suffix.lower().lstrip('.')
            
            # إنشاء مسار الملف الجديد
            output_path = os.path.join(
                self.temp_dir,
                f"converted_{int(time.time())}.{output_format}"
            )
            
            if input_format == 'txt' and output_format == 'pdf':
                return await self._txt_to_pdf(input_path, output_path)
            elif input_format == 'docx' and output_format == 'txt':
                return await self._docx_to_txt(input_path, output_path)
            elif input_format == 'pdf' and output_format == 'txt':
                return await self._pdf_to_txt(input_path, output_path)
            else:
                logger.warning(f"تحويل غير مدعوم: {input_format} إلى {output_format}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في تحويل المستند: {e}")
            return None
    
    async def _txt_to_pdf(self, input_path: str, output_path: str) -> Optional[str]:
        """تحويل TXT إلى PDF"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            
            # قراءة النص
            with open(input_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            # إنشاء PDF
            c = canvas.Canvas(output_path, pagesize=letter)
            width, height = letter
            
            # كتابة النص
            y = height - 50
            for line in text.split('\n'):
                if y < 50:  # صفحة جديدة
                    c.showPage()
                    y = height - 50
                c.drawString(50, y, line[:80])  # تحديد طول السطر
                y -= 20
            
            c.save()
            return output_path
            
        except ImportError:
            logger.error("مكتبة reportlab غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل TXT إلى PDF: {e}")
            return None
    
    async def _docx_to_txt(self, input_path: str, output_path: str) -> Optional[str]:
        """تحويل DOCX إلى TXT"""
        try:
            from docx import Document
            
            # قراءة المستند
            doc = Document(input_path)
            
            # استخراج النص
            text = []
            for paragraph in doc.paragraphs:
                text.append(paragraph.text)
            
            # حفظ النص
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(text))
            
            return output_path
            
        except ImportError:
            logger.error("مكتبة python-docx غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل DOCX إلى TXT: {e}")
            return None
    
    async def _pdf_to_txt(self, input_path: str, output_path: str) -> Optional[str]:
        """تحويل PDF إلى TXT"""
        try:
            import PyPDF2
            
            text = []
            with open(input_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    text.append(page.extract_text())
            
            # حفظ النص
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(text))
            
            return output_path
            
        except ImportError:
            logger.error("مكتبة PyPDF2 غير مثبتة")
            return None
        except Exception as e:
            logger.error(f"خطأ في تحويل PDF إلى TXT: {e}")
            return None
    
    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المؤقتة: {e}")

# إنشاء مثيل عام للمحول
file_converter = FileConverter()
