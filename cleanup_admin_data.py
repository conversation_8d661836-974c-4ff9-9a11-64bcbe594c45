#!/usr/bin/env python3
"""
سكريبت تنظيف بيانات المشرفين من الملفات
يحذف بيانات المشرفين من user_links.js و user_statistics.json
"""

import json
import os
import re
from datetime import datetime

# معرفات المستخدمين المراد حذفها
ADMIN_IDS_TO_REMOVE = [6719024416]  # حسابك الشخصي

def cleanup_user_links():
    """تنظيف ملف user_links.js من بيانات المشرفين"""
    try:
        file_path = "user_links.js"
        if not os.path.exists(file_path):
            print(f"❌ الملف {file_path} غير موجود")
            return False
        
        print(f"🔍 جاري تنظيف ملف {file_path}...")
        
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استخراج البيانات من بين علامات JSON
        start_marker = "/*JSON_START*/"
        end_marker = "/*JSON_END*/"
        start = content.find(start_marker)
        end = content.find(end_marker)
        
        if start == -1 or end == -1:
            print("❌ لم يتم العثور على علامات JSON في الملف")
            return False
        
        start += len(start_marker)
        json_data = content[start:end].strip()
        
        # تحويل إلى JSON
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            print(f"❌ خطأ في تحليل JSON: {e}")
            return False
        
        # عدد البيانات قبل التنظيف
        original_count = len(data)
        print(f"📊 عدد الروابط قبل التنظيف: {original_count}")
        
        # فلترة البيانات (حذف بيانات المشرفين)
        filtered_data = []
        removed_count = 0
        
        for item in data:
            user_id = item.get('user_id')
            if user_id not in ADMIN_IDS_TO_REMOVE:
                filtered_data.append(item)
            else:
                removed_count += 1
        
        print(f"🗑️ تم حذف {removed_count} رابط للمشرفين")
        print(f"✅ عدد الروابط بعد التنظيف: {len(filtered_data)}")
        
        # إعادة كتابة الملف
        new_json_data = json.dumps(filtered_data, ensure_ascii=False, indent=2)
        
        # إحصائيات جديدة
        unique_users = len(set(item['user_id'] for item in filtered_data))
        unique_channels = len(set(item['channel'] for item in filtered_data if item['channel'] != 'غير معروف'))
        
        js_content = f"""// ملف تسجيل روابط المستخدمين - تم إنشاؤه تلقائياً
// آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// تم تنظيف بيانات المشرفين

// بيانات الروابط في تنسيق JSON
/*JSON_START*/
{new_json_data}
/*JSON_END*/

// إحصائيات سريعة
const stats = {{
    totalLinks: {len(filtered_data)},
    lastUpdate: "{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}",
    uniqueUsers: {unique_users},
    uniqueChannels: {unique_channels},
    adminDataRemoved: true
}};

// تحميل البيانات من JSON
const userLinks = {new_json_data};

// وظائف مساعدة
function getUserLinks(userId) {{
    return userLinks.filter(link => link.user_id === userId);
}}

function getChannelLinks(channelName) {{
    return userLinks.filter(link => link.channel === channelName);
}}

function getLinksToday() {{
    const today = new Date().toISOString().split('T')[0];
    return userLinks.filter(link => link.timestamp.startsWith(today));
}}

console.log('تم تحميل', userLinks.length, 'رابط من', stats.uniqueUsers, 'مستخدم (بعد حذف بيانات المشرفين)');
"""
        
        # حفظ الملف المنظف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ تم تنظيف ملف {file_path} بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف ملف user_links.js: {e}")
        return False

def cleanup_user_statistics():
    """تنظيف ملف user_statistics.json من بيانات المشرفين"""
    try:
        file_path = "user_statistics.json"
        if not os.path.exists(file_path):
            print(f"❌ الملف {file_path} غير موجود")
            return False
        
        print(f"🔍 جاري تنظيف ملف {file_path}...")
        
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # عدد المستخدمين قبل التنظيف
        original_users = len(data.get('users', {}))
        print(f"📊 عدد المستخدمين قبل التنظيف: {original_users}")
        
        # حذف بيانات المشرفين
        users = data.get('users', {})
        removed_users = []
        
        for admin_id in ADMIN_IDS_TO_REMOVE:
            admin_id_str = str(admin_id)
            if admin_id_str in users:
                user_info = users[admin_id_str]
                removed_users.append(f"{user_info.get('first_name', 'غير معروف')} (ID: {admin_id})")
                del users[admin_id_str]
        
        # تحديث العدد الإجمالي
        data['total_users'] = len(users)
        data['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # إضافة ملاحظة التنظيف
        data['admin_data_cleaned'] = {
            "cleaned_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "removed_users": removed_users,
            "removed_count": len(removed_users)
        }
        
        print(f"🗑️ تم حذف {len(removed_users)} مستخدم مشرف:")
        for user in removed_users:
            print(f"   - {user}")
        
        print(f"✅ عدد المستخدمين بعد التنظيف: {len(users)}")
        
        # حفظ الملف المنظف
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم تنظيف ملف {file_path} بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف ملف user_statistics.json: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧹 بدء عملية تنظيف بيانات المشرفين...")
    print("=" * 50)
    
    # تنظيف ملف الروابط
    links_cleaned = cleanup_user_links()
    print()
    
    # تنظيف ملف الإحصائيات
    stats_cleaned = cleanup_user_statistics()
    print()
    
    # النتيجة النهائية
    print("=" * 50)
    if links_cleaned and stats_cleaned:
        print("🎉 تم تنظيف جميع الملفات بنجاح!")
        print("✅ لن تظهر بيانات المشرفين في الإحصائيات أو ملف الروابط")
    elif links_cleaned or stats_cleaned:
        print("⚠️ تم تنظيف بعض الملفات فقط")
        if links_cleaned:
            print("✅ تم تنظيف ملف الروابط")
        if stats_cleaned:
            print("✅ تم تنظيف ملف الإحصائيات")
    else:
        print("❌ فشل في تنظيف الملفات")
    
    print("\n💡 ملاحظة: البيانات الجديدة لن تُسجل للمشرفين تلقائياً")

if __name__ == "__main__":
    main()
