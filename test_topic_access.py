#!/usr/bin/env python3
"""
اختبار طرق مختلفة للوصول لمنشورات المواضيع في تليجرام
"""

import asyncio
import logging
from pyrogram import Client
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# معلومات الاختبار
TEST_CHANNEL = "MasterfulCommunity"
TEST_MESSAGE_ID = 7000
TEST_TOPIC_ID = 13

async def test_topic_access():
    """اختبار طرق مختلفة للوصول لمنشورات المواضيع"""
    
    # إنشاء عميل البوت
    app = Client(
        "test_bot",
        api_id=int(os.getenv("API_ID")),
        api_hash=os.getenv("API_HASH"),
        bot_token=os.getenv("BOT_TOKEN")
    )
    
    async with app:
        print(f"🧪 اختبار الوصول للمنشور {TEST_MESSAGE_ID} في الموضوع {TEST_TOPIC_ID} من قناة {TEST_CHANNEL}")
        print("=" * 80)
        
        # الطريقة 1: السحب العادي
        print("\n1️⃣ الطريقة الأولى: السحب العادي")
        try:
            msg = await app.get_messages(TEST_CHANNEL, TEST_MESSAGE_ID)
            if msg and not msg.empty:
                print(f"✅ نجح! نوع المحتوى: {type(msg.media) if msg.media else 'نص'}")
                print(f"📄 النص: {msg.text[:100] if msg.text else 'لا يوجد نص'}...")
                return msg
            else:
                print("❌ فشل - المنشور فارغ أو غير موجود")
        except Exception as e:
            print(f"❌ فشل - خطأ: {e}")
        
        # الطريقة 2: البحث في التاريخ
        print("\n2️⃣ الطريقة الثانية: البحث في تاريخ المحادثة")
        try:
            found = False
            async for message in app.get_chat_history(TEST_CHANNEL, limit=200):
                if message.id == TEST_MESSAGE_ID:
                    print(f"✅ تم العثور على المنشور في التاريخ!")
                    print(f"📄 النص: {message.text[:100] if message.text else 'لا يوجد نص'}...")
                    print(f"🎬 نوع المحتوى: {type(message.media) if message.media else 'نص'}")
                    found = True
                    return message
            
            if not found:
                print("❌ لم يتم العثور على المنشور في التاريخ")
        except Exception as e:
            print(f"❌ فشل - خطأ: {e}")
        
        # الطريقة 3: استخدام معرف القناة الرقمي
        print("\n3️⃣ الطريقة الثالثة: استخدام معرف القناة الرقمي")
        try:
            chat = await app.get_chat(TEST_CHANNEL)
            print(f"📺 معرف القناة الرقمي: {chat.id}")
            
            msg = await app.get_messages(chat.id, TEST_MESSAGE_ID)
            if msg and not msg.empty:
                print(f"✅ نجح بالمعرف الرقمي!")
                print(f"📄 النص: {msg.text[:100] if msg.text else 'لا يوجد نص'}...")
                return msg
            else:
                print("❌ فشل - المنشور فارغ أو غير موجود")
        except Exception as e:
            print(f"❌ فشل - خطأ: {e}")
        
        # الطريقة 4: البحث النطاقي
        print("\n4️⃣ الطريقة الرابعة: البحث النطاقي")
        try:
            search_range = list(range(max(1, TEST_MESSAGE_ID - 20), TEST_MESSAGE_ID + 21))
            print(f"🔍 البحث في النطاق: {search_range[0]} إلى {search_range[-1]}")
            
            messages = await app.get_messages(TEST_CHANNEL, search_range)
            
            for msg in messages:
                if msg and msg.id == TEST_MESSAGE_ID:
                    print(f"✅ تم العثور على المنشور في البحث النطاقي!")
                    print(f"📄 النص: {msg.text[:100] if msg.text else 'لا يوجد نص'}...")
                    return msg
            
            print("❌ لم يتم العثور على المنشور في النطاق")
        except Exception as e:
            print(f"❌ فشل - خطأ: {e}")
        
        # الطريقة 5: معلومات القناة والمواضيع
        print("\n5️⃣ الطريقة الخامسة: فحص معلومات القناة")
        try:
            chat = await app.get_chat(TEST_CHANNEL)
            print(f"📺 اسم القناة: {chat.title}")
            print(f"🆔 معرف القناة: {chat.id}")
            print(f"👥 نوع المحادثة: {chat.type}")
            print(f"📊 عدد الأعضاء: {chat.members_count if hasattr(chat, 'members_count') else 'غير معروف'}")
            
            # محاولة الحصول على معلومات إضافية
            full_chat = await app.get_chat(chat.id)
            print(f"📋 الوصف: {full_chat.description[:100] if full_chat.description else 'لا يوجد وصف'}...")
            
        except Exception as e:
            print(f"❌ فشل في الحصول على معلومات القناة: {e}")
        
        print("\n❌ فشلت جميع الطرق في العثور على المنشور")
        return None

async def test_alternative_methods():
    """اختبار طرق بديلة"""
    
    app = Client(
        "test_bot",
        api_id=int(os.getenv("API_ID")),
        api_hash=os.getenv("API_HASH"),
        bot_token=os.getenv("BOT_TOKEN")
    )
    
    async with app:
        print("\n\n🔬 اختبار طرق بديلة:")
        print("=" * 50)
        
        # اختبار منشور عادي أولاً
        print("\n🧪 اختبار منشور عادي (رقم 31):")
        try:
            msg = await app.get_messages(TEST_CHANNEL, 31)
            if msg and not msg.empty:
                print("✅ المنشور العادي يعمل بشكل طبيعي")
                print(f"📄 النص: {msg.text[:100] if msg.text else 'لا يوجد نص'}...")
            else:
                print("❌ حتى المنشور العادي لا يعمل")
        except Exception as e:
            print(f"❌ خطأ في المنشور العادي: {e}")
        
        # اختبار الوصول للقناة
        print("\n🧪 اختبار الوصول للقناة:")
        try:
            chat = await app.get_chat(TEST_CHANNEL)
            print(f"✅ يمكن الوصول للقناة: {chat.title}")
            
            # اختبار الحصول على آخر المنشورات
            print("\n📋 آخر 5 منشورات:")
            count = 0
            async for message in app.get_chat_history(TEST_CHANNEL, limit=5):
                count += 1
                print(f"  {count}. المنشور {message.id}: {message.text[:50] if message.text else 'محتوى وسائط'}...")
                
        except Exception as e:
            print(f"❌ خطأ في الوصول للقناة: {e}")

if __name__ == "__main__":
    print("🚀 بدء اختبار الوصول لمنشورات المواضيع...")
    
    # تشغيل الاختبار الأساسي
    result = asyncio.run(test_topic_access())
    
    # تشغيل الاختبارات البديلة
    asyncio.run(test_alternative_methods())
    
    print("\n🏁 انتهى الاختبار!")
    
    if result:
        print("✅ تم العثور على المنشور بإحدى الطرق")
    else:
        print("❌ لم يتم العثور على المنشور بأي طريقة")
        print("\n💡 اقتراحات:")
        print("  • تأكد من أن المنشور موجود فعلاً")
        print("  • تأكد من أن البوت يملك صلاحيات الوصول")
        print("  • جرب منشور آخر من نفس القناة")
        print("  • تأكد من أن القناة عامة")
